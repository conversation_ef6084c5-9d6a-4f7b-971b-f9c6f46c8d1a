# Generated by Django 3.2 on 2021-05-31 14:17

from django.db import migrations
from geonode.base.models import ResourceBaseManager


def copy_doc_files(apps, _):
    Document = apps.get_model('documents', 'Document')
    for _doc in Document.objects.all():
        if _doc.doc_file:
            ResourceBaseManager.upload_files(
                resource_id=_doc.resourcebase_ptr_id, files=[_doc.doc_file.path, ], force=True)


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0031_auto_20201107_2241'),
        ('base', '0062_resourcebase_files'),
    ]

    operations = [
        migrations.RunPython(copy_doc_files, migrations.RunPython.noop),
        migrations.RemoveField(
            model_name='document',
            name='doc_file',
        ),
    ]
