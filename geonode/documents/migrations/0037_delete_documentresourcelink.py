# Generated by Django 3.2.21 on 2023-10-05 15:16

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("documents", "0036_clean_document_thumbnails"),
        ("base", "0086_linkedresource"),
    ]

    operations = [
        migrations.RunSQL(
            "INSERT INTO base_linkedresource(source_id, target_id, internal) "
            "SELECT document_id, object_id, false as internal "
            "FROM documents_documentresourcelink "
            "WHERE EXISTS (SELECT 1 FROM base_resourcebase base WHERE base.id=object_id);"
        ),
        migrations.DeleteModel(
            name="DocumentResourceLink",
        ),
    ]
