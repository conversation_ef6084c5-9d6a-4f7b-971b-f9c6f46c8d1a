# Generated by Django 2.2.16 on 2021-04-15 13:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Harvester',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Harvester name', max_length=100)),
                ('remote_url', models.URLField(help_text='Base URL of the remote service that is to be harvested')),
                ('scheduling_enabled', models.BooleanField(default=True, help_text='Whether to periodically schedule this harvester to look for resources on the remote service')),
                ('update_frequency', models.PositiveIntegerField(default=60, help_text='How often (in minutes) should new harvesting sessions be automatically scheduled? Setting this value to zero has the same effect as setting `scheduling_enabled` to False ')),
                ('default_access_permissions', models.J<PERSON><PERSON>ield(default=dict, help_text='Default access permissions of harvested resources')),
                ('harvester_type', models.CharField(choices=[('geonode.harvesting.harvesters.geonode.GeonodeHarvester', 'geonode.harvesting.harvesters.geonode.GeonodeHarvester')], default='geonode.harvesting.harvesters.geonode.GeonodeHarvester', help_text='Harvester class used to perform harvesting sessions', max_length=255)),
                ('harvester_type_specific_configuration', models.JSONField(default=dict, help_text='Configuration specific to each harvester type. Please consult GeoNode documentation on harvesting for more info.')),
                ('default_owner', models.ForeignKey(help_text='Default owner of harvested resources', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HarvestingSession',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('started', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('ended', models.DateTimeField(blank=True, null=True)),
                ('records_harvested', models.IntegerField(default=0)),
                ('harvester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='harvesting.Harvester')),
            ],
        ),
    ]
