# Generated by Django 2.2.16 on 2021-04-15 17:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(choices=[('geonode.harvesting.harvesters.geonode.GeonodeHarvester', 'geonode.harvesting.harvesters.geonode.GeonodeHarvester')], default='geonode.harvesting.harvesters.geonode.GeonodeHarvester', help_text='Harvester class used to perform harvesting sessions. New harvester types can be added by an admin by changing the main GeoNode `settings.py` file', max_length=255),
        ),
        migrations.AlterField(
            model_name='harvestingsession',
            name='harvester',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='harvesting_sessions', to='harvesting.Harvester'),
        ),
    ]
