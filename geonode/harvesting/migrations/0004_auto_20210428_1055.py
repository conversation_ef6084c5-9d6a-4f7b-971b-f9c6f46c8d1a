# Generated by Django 2.2.16 on 2021-04-28 10:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0003_harvestingsession_total_records_found'),
    ]

    operations = [
        migrations.AddField(
            model_name='harvester',
            name='check_availability_frequency',
            field=models.PositiveIntegerField(default=30, help_text='How often (in minutes) should the remote service be checked for availability?'),
        ),
        migrations.AddField(
            model_name='harvester',
            name='last_checked_availability',
            field=models.DateTimeField(blank=True, help_text='Last time the remote server was checked for availability', null=True),
        ),
        migrations.AddField(
            model_name='harvester',
            name='remote_available',
            field=models.BooleanField(default=False, help_text='Whether the remote service is known to be available or not'),
        ),
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(choices=[('geonode.harvesting.harvesters.geonode.GeonodeLegacyHarvester', 'geonode.harvesting.harvesters.geonode.GeonodeLegacyHarvester'), ('geonode.harvesting.harvesters.wms.OgcWmsHarvester', 'geonode.harvesting.harvesters.wms.OgcWmsHarvester')], default='geonode.harvesting.harvesters.geonode.GeonodeLegacyHarvester', help_text='Harvester class used to perform harvesting sessions. New harvester types can be added by an admin by changing the main GeoNode `settings.py` file', max_length=255),
        ),
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type_specific_configuration',
            field=models.JSONField(default=dict, help_text='Configuration specific to each harvester type. Please consult GeoNode documentation on harvesting for more info. This field is mandatory, so at the very least an empty object (i.e. {}) must be supplied.'),
        ),
    ]
