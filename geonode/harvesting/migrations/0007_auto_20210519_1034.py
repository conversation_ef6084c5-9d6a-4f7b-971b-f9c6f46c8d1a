# Generated by Django 2.2.16 on 2021-05-19 10:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0006_harvester_availability_check_task'),
    ]

    operations = [
        migrations.AddField(
            model_name='harvester',
            name='delete_orphan_resources_automatically',
            field=models.BooleanField(default=False, help_text='Orphan resources are those that have previously been created by means of a harvesting operation but that GeoNode can no longer find on the remote service being harvested. Should these resources be deleted from GeoNode automatically? This also applies to when a harvester configuration is deleted, in which case all of the resources that originated from that harvester are now considered to be orphan.'),
        ),
        migrations.AddField(
            model_name='harvester',
            name='harvest_new_resources_by_default',
            field=models.BooleanField(default=False, help_text='Should new resources be harvested automatically without explicit selection?'),
        ),
        migrations.AddField(
            model_name='harvester',
            name='last_updated',
            field=models.DateTimeField(auto_now=True, help_text='Date of last update to the harvester configuration.'),
        ),
    ]
