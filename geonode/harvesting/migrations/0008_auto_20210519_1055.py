# Generated by Django 2.2.16 on 2021-05-19 10:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('base', '0058_thesaurus_order'),
        ('harvesting', '0007_auto_20210519_1034'),
    ]

    operations = [
        migrations.CreateModel(
            name='HarvestableResource',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unique_identifier', models.CharField(help_text='Identifier that allows referencing the resource on its remote service in a unique fashion. This is usually automatically filled by the harvester worker. The harvester worker needs to know how to either read or generate this from each remote resource in order to be able to compare the availability of resources between consecutive harvesting sessions.', max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('geonode_resource', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='base.ResourceBase')),
                ('harvester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='harvesting.Harvester')),
            ],
        ),
        migrations.AddConstraint(
            model_name='harvestableresource',
            constraint=models.UniqueConstraint(fields=('harvester', 'unique_identifier'), name='unique_id_for_harvester'),
        ),
    ]
