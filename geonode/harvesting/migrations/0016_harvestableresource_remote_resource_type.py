# Generated by Django 2.2.16 on 2021-06-04 00:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0015_harvestableresource_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='harvestableresource',
            name='remote_resource_type',
            field=models.CharField(blank=True, help_text='Type of the resource in the remote service. Each harvester worker knows how to fill this field, in accordance with the resources for which harvesting is supported', max_length=255),
        ),
    ]
