# Generated by Django 3.2 on 2021-06-14 15:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0016_harvestableresource_remote_resource_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='default_access_permissions',
            field=models.J<PERSON><PERSON>ield(help_text='Default access permissions of harvested resources'),
        ),
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type_specific_configuration',
            field=models.JSONField(help_text='Configuration specific to each harvester type. Please consult GeoNode documentation on harvesting for more info. This field is mandatory, so at the very least an empty object (i.e. {}) must be supplied.'),
        ),
    ]
