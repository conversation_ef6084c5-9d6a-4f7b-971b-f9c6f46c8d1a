# Generated by Django 3.2.4 on 2021-06-28 18:26
# Hand edited in order to set choices for `modelsHarvester.harvester_type` to come from settings in a dynamic fashion
# This shall prevent Django autogenerating new migration files for `geonode.harvesting`
# whenever new custom harvester classes are added to the settings

from django.db import migrations, models

from .. import config


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0028_harvester_num_harvestable_resources'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(
                choices=[(value, value) for value in config.get_setting("HARVESTER_CLASSES")],
                default='geonode.harvesting.harvesters.geonode.GeonodeLegacyHarvester',
                help_text=(
                    'Harvester class used to perform harvesting sessions. New harvester types can be added by an admin by changing the '
                    'main GeoNode `settings.py` file'
                ),
                max_length=255
            ),
        ),
    ]
