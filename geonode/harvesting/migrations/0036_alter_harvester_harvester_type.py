# Generated by Django 3.2.4 on 2021-09-22 13:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0035_replace_geonode_harvester_module_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(
                choices=[
                    (
                        'geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester',
                        'geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester'
                    ),
                    (
                        'geonode.harvesting.harvesters.wms.OgcWmsHarvester',
                        'geonode.harvesting.harvesters.wms.OgcWmsHarvester'
                    ),
                ],
                default='geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester',
                help_text=(
                    'Harvester class used to perform harvesting sessions. New harvester types can be '
                    'added by an admin by changing the main GeoNode `settings.py` file'
                ),
                max_length=255
            ),
        ),
    ]
