# Generated by Django 3.2.4 on 2021-09-24 09:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0037_alter_harvester_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='status',
            field=models.CharField(
                choices=[
                    ('ready', 'ready'),
                    ('updating-harvestable-resources', 'updating-harvestable-resources'),
                    ('aborting-update-harvestable-resources', 'aborting-update-harvestable-resources'),
                    ('harvesting-resources', 'harvesting-resources'),
                    ('aborting-harvesting-resources', 'aborting-harvesting-resources'),
                    ('checking-availability', 'checking-availability')
                ],
                default='ready',
                max_length=50
            ),
        ),
    ]
