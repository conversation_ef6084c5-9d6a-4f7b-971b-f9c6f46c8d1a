# Generated by Django 3.2.4 on 2021-09-30 11:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0042_auto_20210930_1155'),
    ]

    operations = [
        migrations.AddField(
            model_name='harvester',
            name='harvesting_session_update_frequency',
            field=models.PositiveIntegerField(default=60, help_text='How often (in minutes) should new harvesting sessions be automatically scheduled?'),
        ),
    ]
