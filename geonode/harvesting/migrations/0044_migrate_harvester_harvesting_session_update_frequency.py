# Generated by Django 3.2.4 on 2021-09-30 12:01

from django.db import migrations


def migrate_harvesting_session_update_frequency(apps, schema_editor):
    harvester_model = apps.get_model("harvesting", "Harvester")
    for harvester in harvester_model.objects.all():
        harvester.harvesting_session_update_frequency = harvester.update_frequency
        harvester.save()


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0043_harvester_harvesting_session_update_frequency'),
    ]

    operations = [
        migrations.RunPython(migrate_harvesting_session_update_frequency, reverse_code=migrations.RunPython.noop),
    ]
