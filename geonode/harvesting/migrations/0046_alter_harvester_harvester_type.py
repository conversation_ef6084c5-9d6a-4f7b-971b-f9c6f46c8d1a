# Generated by Django 3.2.7 on 2021-10-08 22:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0045_remove_harvester_update_frequency'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(choices=[('geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker', 'geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker'), ('geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester', 'geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester'), ('geonode.harvesting.harvesters.geonodeharvester.GeonodeCurrentHarvester', 'geonode.harvesting.harvesters.geonodeharvester.GeonodeCurrentHarvester'), ('geonode.harvesting.harvesters.wms.OgcWmsHarvester', 'geonode.harvesting.harvesters.wms.OgcWmsHarvester')], default='geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker', help_text='Harvester class used to perform harvesting sessions. New harvester types can be added by an admin by changing the main GeoNode `settings.py` file', max_length=255),
        ),
    ]
