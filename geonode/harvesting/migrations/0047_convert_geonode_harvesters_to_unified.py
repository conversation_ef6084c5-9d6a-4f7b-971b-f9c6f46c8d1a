# Generated by Django 3.2.7 on 2021-10-11 11:51

from django.db import migrations

_INDIVIDUAL_HARVESTER_PATHS = [
    "geonode.harvesting.harvesters.geonodeharvester.GeonodeLegacyHarvester",
    "geonode.harvesting.harvesters.geonodeharvester.GeonodeCurrentHarvester",
]
_UNIFIED_HARVESTER_PATH = "geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker"


def convert_geonode_harvesters_to_unified(apps, schema_editor):
    """Convert GeoNode harvesters to use the unified harvester"""
    harvester_model = apps.get_model('harvesting', 'Harvester')
    for harvester in harvester_model.objects.all():
        if harvester.harvester_type in _INDIVIDUAL_HARVESTER_PATHS:
            harvester.harvester_type = _UNIFIED_HARVESTER_PATH
            harvester.save()


def reverse_harvester_conversion(apps, schema_editor):
    harvester_model = apps.get_model('harvesting', 'Harvester')
    for harvester in harvester_model.objects.all():
        if harvester.harvester_type == _UNIFIED_HARVESTER_PATH:
            harvester.harvester_type = _INDIVIDUAL_HARVESTER_PATHS[0]
            harvester.save()


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0046_alter_harvester_harvester_type'),
    ]

    operations = [
        migrations.RunPython(convert_geonode_harvesters_to_unified, reverse_code=reverse_harvester_conversion),
    ]
