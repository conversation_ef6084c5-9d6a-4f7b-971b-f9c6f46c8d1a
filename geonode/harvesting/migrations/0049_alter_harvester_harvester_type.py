# Generated by Django 3.2.4 on 2021-10-12 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('harvesting', '0048_alter_harvester_harvester_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='harvester',
            name='harvester_type',
            field=models.CharField(choices=[('geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker', 'geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker'), ('geonode.harvesting.harvesters.wms.OgcWmsHarvester', 'geonode.harvesting.harvesters.wms.OgcWmsHarvester'), ('geonode.harvesting.harvesters.arcgis.ArcgisHarvesterWorker', 'geonode.harvesting.harvesters.arcgis.ArcgisHarvesterWorker')], default='geonode.harvesting.harvesters.geonodeharvester.GeonodeUnifiedHarvesterWorker', help_text='Harvester class used to perform harvesting sessions. New harvester types can be added by an admin by changing the main GeoNode `settings.py` file', max_length=255),
        ),
    ]
