# Generated by Django 3.2.4 on 2021-09-30 11:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ManagementCommandJob',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command', models.CharField(max_length=250)),
                ('app_name', models.CharField(max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('start_time', models.DateTimeField(null=True)),
                ('end_time', models.DateTimeField(null=True)),
                ('modified_at', models.DateTimeField(auto_now=True)),
                ('args', models.JSONField(blank=True, default=list, help_text='JSON encoded positional arguments (Example: ["arg1", "arg2"])', verbose_name='Positional Arguments')),
                ('kwargs', models.J<PERSON><PERSON>ield(blank=True, default=dict, help_text='JSON encoded keyword arguments (Example: {"argument": "value"})', verbose_name='Keyword Arguments')),
                ('celery_result_id', models.UUIDField(blank=True, null=True)),
                ('output_message', models.TextField(null=True)),
                ('status', models.CharField(choices=[('CREATED', 'Created'), ('QUEUED', 'Queued'), ('STARTED', 'Started'), ('FINISHED', 'Finished')], default='CREATED', max_length=8)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
