# Generated by Django 3.2.4 on 2021-06-23 12:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='AbstractProcessingTask',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('is_enabled', models.BooleanField(default=True, help_text='Disabling this Task will make the Processing Workflow to skip it.')),
                ('polymorphic_ctype', models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='polymorphic_geonode_resource_processing.abstractprocessingtask_set+', to='contenttypes.contenttype')),
            ],
            options={
                'abstract': False,
                'base_manager_name': 'objects',
            },
        ),
        migrations.CreateModel(
            name='ProcessingWorkflow',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('is_enabled', models.BooleanField(default=True, help_text='Disabling this Task will make the Processing Workflow to skip it.')),
            ],
        ),
        migrations.CreateModel(
            name='SampleProcessingTask',
            fields=[
                ('abstractprocessingtask_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='geonode_resource_processing.abstractprocessingtask')),
            ],
            options={
                'abstract': False,
                'base_manager_name': 'objects',
            },
            bases=('geonode_resource_processing.abstractprocessingtask',),
        ),
        migrations.CreateModel(
            name='ProcessingWorkflowTasks',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField()),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='link_to_workflow', to='geonode_resource_processing.abstractprocessingtask')),
                ('workflow', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='geonode_resource_processing.processingworkflow')),
            ],
            options={
                'ordering': ('order',),
            },
        ),
        migrations.AddField(
            model_name='processingworkflow',
            name='processing_tasks',
            field=models.ManyToManyField(blank=True, through='geonode_resource_processing.ProcessingWorkflowTasks', to='geonode_resource_processing.AbstractProcessingTask'),
        ),
    ]
