# Generated by Django 4.2.9 on 2024-01-10 14:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("geonode_resource_processing", "0002_alter_processingworkflowtasks_options"),
    ]

    operations = [
        migrations.AlterField(
            model_name="abstractprocessingtask",
            name="polymorphic_ctype",
            field=models.ForeignKey(
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="polymorphic_%(app_label)s.%(class)s_set+",
                to="contenttypes.contenttype",
            ),
        ),
    ]
